# Sentry Integration Setup & Usage Guide

## Overview

Sentry is integrated into the backend to provide comprehensive error monitoring, performance tracking, and debugging capabilities. This guide covers setup, configuration, and best practices.

## Features Implemented

✅ **Error Monitoring**: Automatic capture of unhandled errors and exceptions  
✅ **Performance Tracking**: Request tracing and performance monitoring  
✅ **Context Enrichment**: Request details, user info, and custom context  
✅ **Security**: Sensitive data filtering (auth headers, cookies)  
✅ **Environment Support**: Different configurations for dev/prod  
✅ **Graceful Shutdown**: Proper Sentry flushing on app termination  

## Setup Instructions

### 1. Get Sentry DSN

1. Go to [Sentry.io](https://sentry.io) and create an account
2. Create a new project and select "Bun" as the platform
3. Copy your DSN (Data Source Name) - it looks like:
   ```
   https://<EMAIL>/123456
   ```

### 2. Configure Environment Variables

Add your Sentry DSN to your environment files:

**Development (.env):**
```bash
SENTRY_DSN=https://<EMAIL>/project-id
```

**Production (.env.easypanel):**
```bash
SENTRY_DSN=https://<EMAIL>/project-id
```

**Important Notes:**
- The SENTRY_DSN is required - the server will not start without it
- Replace the example DSN with your actual Sentry project DSN
- Use different DSNs for development and production environments

### 3. Verify Installation

The Sentry SDK is already installed and configured. To verify it's working:

1. Start the development server:
   ```bash
   bun run dev
   ```

2. Test error reporting (development only):
   ```bash
   # Test basic error
   curl http://localhost:8080/sentry/test-error
   
   # Test async error
   curl http://localhost:8080/sentry/test-async-error
   
   # Test manual capture
   curl http://localhost:8080/sentry/test-manual-capture
   
   # Test with custom context
   curl http://localhost:8080/sentry/test-context
   
   # Test performance monitoring
   curl http://localhost:8080/sentry/test-performance

   # Test Prisma error handling
   curl http://localhost:8080/sentry/test-prisma-error

   # Test Prisma query tracing
   curl http://localhost:8080/sentry/test-prisma-tracing
   ```

3. Check your Sentry dashboard - errors should appear within seconds

## Configuration Details

### Initialization Settings

```typescript
Sentry.init({
  dsn: getSentryDsn(),
  environment: process.env.NODE_ENV || 'development',
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
  integrations: [
    Sentry.prismaIntegration(), // Database query tracing and error monitoring
  ],
  // Filter sensitive data from being sent to Sentry
  beforeSend(event) {
    // Remove sensitive headers
    if (event.request?.headers) {
      delete event.request.headers.authorization;
      delete event.request.headers.cookie;
      delete event.request.headers['x-session-token'];
    }
    return event;
  },
});
```

### Sample Rates

- **Development**: 100% of transactions traced
- **Production**: 10% of transactions traced (to reduce overhead)

### Security Features

Sensitive headers are automatically filtered:
- `authorization`
- `cookie` 
- `x-session-token`

## Usage Examples

### Manual Error Capture

```typescript
import * as Sentry from "@sentry/bun";

try {
  // Your code here
} catch (error) {
  Sentry.captureException(error);
}
```

### Adding Custom Context

```typescript
Sentry.withScope((scope) => {
  scope.setTag("feature", "payment");
  scope.setUser({ id: userId, email: userEmail });
  scope.setContext("payment_data", { amount, currency });
  
  Sentry.captureException(error);
});
```

### Performance Monitoring

```typescript
const transaction = Sentry.startTransaction({
  op: "payment.process",
  name: "Process Payment",
});

try {
  const span = transaction.startChild({
    op: "db.query",
    description: "Save payment to database",
  });
  
  // Your database operation
  span.finish();
  
} finally {
  transaction.finish();
}
```

## Best Practices

### 1. Don't Report Expected Errors

```typescript
// ❌ Don't report validation errors
if (!email) {
  throw new HTTPException(400, { message: "Email required" });
}

// ✅ Report unexpected system errors
try {
  await database.save(user);
} catch (error) {
  Sentry.captureException(error);
  throw new HTTPException(500, { message: "Internal server error" });
}
```

### 2. Add Meaningful Context

```typescript
Sentry.withScope((scope) => {
  scope.setTag("operation", "user_registration");
  scope.setUser({ id: userId });
  scope.setContext("registration_data", {
    source: "web",
    timestamp: new Date().toISOString(),
  });
  
  Sentry.captureException(error);
});
```

### 3. Use Appropriate Log Levels

```typescript
// For warnings
scope.setLevel("warning");

// For errors (default)
scope.setLevel("error");

// For info
scope.setLevel("info");
```

## Monitoring & Alerts

### Key Metrics to Monitor

1. **Error Rate**: Percentage of requests that result in errors
2. **Response Time**: P95 response times for critical endpoints
3. **Database Performance**: Slow query detection
4. **Memory Usage**: Memory leaks and high usage patterns

### Setting Up Alerts

1. Go to your Sentry project settings
2. Navigate to "Alerts" section
3. Create alerts for:
   - New error types
   - Error rate spikes
   - Performance degradation
   - High memory usage

## Troubleshooting

### Common Issues

1. **DSN Not Found Error**
   - Check your `.env` file has `SENTRY_DSN` set
   - Verify the DSN format is correct

2. **No Errors Appearing in Dashboard**
   - Check network connectivity
   - Verify DSN is correct
   - Test with `/sentry/test-error` endpoint

3. **Too Many Events**
   - Adjust `tracesSampleRate` in production
   - Add filters for noisy errors

### Debug Mode

Enable debug logging in development:

```typescript
Sentry.init({
  dsn: getSentryDsn(),
  debug: process.env.NODE_ENV === 'development',
  // ... other options
});
```

## Production Considerations

1. **Sample Rate**: Keep `tracesSampleRate` low (0.1 or lower) in production
2. **Data Scrubbing**: Ensure no sensitive data is sent to Sentry
3. **Performance**: Monitor Sentry's impact on application performance
4. **Quotas**: Monitor your Sentry usage to avoid quota limits

## Support

For issues with this integration:
1. Check the Sentry documentation: https://docs.sentry.io/platforms/javascript/guides/bun/
2. Review the middleware code in `server/src/middleware/sentry.ts`
3. Test with the provided test endpoints in development
