import * as Sentry from "@sentry/bun";
import { <PERSON>o } from "hono";
import type { Context } from "hono";

const route = new Hono();

/**
 * Test endpoint to verify Sentry error reporting
 * This endpoint intentionally throws errors to test Sentry integration
 */
route.get("/test-error", (c: Context) => {
  throw new Error("Sentry Bun test - This is an intentional error for testing");
});

/**
 * Test endpoint for async error handling
 */
route.get("/test-async-error", async (c: Context) => {
  await new Promise((resolve, reject) => {
    setTimeout(() => {
      reject(new Error("Sentry async test - This is an intentional async error"));
    }, 100);
  });
});

/**
 * Test endpoint for manual error capture
 */
route.get("/test-manual-capture", (c: Context) => {
  try {
    throw new Error("Manual capture test error");
  } catch (error) {
    Sentry.captureException(error);
    return c.json({ message: "Error captured manually and sent to Sentry" });
  }
});

/**
 * Test endpoint for custom context
 */
route.get("/test-context", (c: Context) => {
  Sentry.withScope((scope) => {
    scope.setTag("test_type", "context_test");
    scope.setLevel("warning");
    scope.setContext("custom_data", {
      test_id: "123",
      user_action: "testing_sentry",
      timestamp: new Date().toISOString(),
    });
    
    Sentry.captureException(new Error("Context test error with custom data"));
  });
  
  return c.json({ message: "Error with custom context sent to Sentry" });
});

/**
 * Test endpoint for performance monitoring
 */
route.get("/test-performance", async (c: Context) => {
  return await Sentry.startSpan({
    op: "test",
    name: "Performance Test",
  }, async (span) => {
    const startTime = Date.now();

    // Simulate database work
    await Sentry.startSpan({
      op: "db.query",
      name: "Simulated database query",
    }, async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
    });

    // Simulate API call
    await Sentry.startSpan({
      op: "http.request",
      name: "Simulated API call",
    }, async () => {
      await new Promise(resolve => setTimeout(resolve, 200));
    });

    return c.json({
      message: "Performance test completed",
      duration: Date.now() - startTime
    });
  });
});

export default route;
