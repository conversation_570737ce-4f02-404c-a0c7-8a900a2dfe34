import "dotenv/config";
import * as Sentry from "@sentry/bun";
import { getSentryDsn } from "./utils/env";

// Initialize Sentry as early as possible
Sentry.init({
  dsn: getSentryDsn(),
  environment: process.env.NODE_ENV || 'development',
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
  // Note: Prisma integration disabled due to compatibility issues with current setup
  // integrations: [
  //   Sentry.prismaIntegration(),
  // ],
});

import { cors } from "hono/cors";
import { Hono } from "hono";
import { authMiddleware } from "./middleware/auth";
import { sentryErrorHandler, sentryTracingMiddleware } from "./middleware/sentry";
import authRoute from "./routes/auth";
import { createInvoice } from "./routes/create-invoice";
import dataRoute from "./routes/data";
import sentryTestRoute from "./routes/sentry-test";
import { xenditWebhook } from "./routes/xendit-webhook";
import { getCorsAllowHeaders, getCorsAllowMethods, getCorsOrigins, getPort } from "./utils/env";

const app = new Hono();

// Sentry middleware - must be first to catch all errors
app.use("*", sentryTracingMiddleware);
app.use("*", sentryErrorHandler);

// CORS Configuration from environment variables
const corsOrigins = getCorsOrigins();
const corsAllowHeaders = getCorsAllowHeaders();
const corsAllowMethods = getCorsAllowMethods();

app.use(
  "*",
  cors({
    origin: [...corsOrigins, "*", "null"],
    allowHeaders: corsAllowHeaders,
    allowMethods: corsAllowMethods,
    credentials: true,
  })
);


app.route("/", authRoute);
app.route("/api/data", dataRoute);

// Add Sentry test routes only in development
if (process.env.NODE_ENV !== 'production') {
  app.route("/sentry", sentryTestRoute);
}

app.use("/create-invoice", authMiddleware);

app.post("/create-invoice", createInvoice);
app.post("/xendit-webhook", xenditWebhook);

const port = getPort();

// Global error handlers for unhandled promise rejections and uncaught exceptions
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  Sentry.captureException(reason);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  Sentry.captureException(error);
  // Don't exit immediately, let Sentry flush
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

console.log(`Server is running on port ${port}`);

// Export app for type inference
export { app };

// For Bun/Cloudflare Workers
export default {
  port,
  fetch: app.fetch,
};

// For Node.js deployment
if (typeof Bun === "undefined") {
  import("@hono/node-server").then(({ serve }) => {
    const server = serve({
      fetch: app.fetch,
      port: Number(port),
    });

    console.log(`HTTP server started on port ${port}`);

    // Keep the process alive
    process.on('SIGTERM', () => {
      console.log('Received SIGTERM, shutting down gracefully');
      server.close(() => {
        // Flush Sentry before exiting
        Sentry.close(2000).then(() => {
          process.exit(0);
        });
      });
    });

    process.on('SIGINT', () => {
      console.log('Received SIGINT, shutting down gracefully');
      server.close(() => {
        // Flush Sentry before exiting
        Sentry.close(2000).then(() => {
          process.exit(0);
        });
      });
    });
  }).catch((error) => {
    console.error('Failed to start server:', error);
    process.exit(1);
  });
}