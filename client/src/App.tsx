import { Route, BrowserRouter as Router, Routes } from 'react-router-dom';
import './index.css';

import Affiliate from './pages/Affiliate';
import Ai from './pages/Ai';
import BeliEmas from './pages/BeliEmas';
import Billing from './pages/Billing';
import Dashboard from './pages/Dashboard';
import Home from './pages/Home';
import Login from './pages/Login';
import Services from './pages/Service';
import Setting from './pages/Setting';
import Support from './pages/Support';
import Templates from './pages/Templates';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/templates" element={<Templates />} />
        <Route path="/login" element={<Login />}></Route>

        {/* Sentry test route (development only) */}
        {process.env.NODE_ENV === 'development' && (
          <Route path="/sentry-test" element={
            <div style={{ padding: '20px', textAlign: 'center' }}>
              <h2>Sentry Error Test</h2>
              <p>Click the button below to test Sentry error reporting:</p>
              <button
                type="button"
                onClick={() => {throw new Error("This is your first error!");}}
                style={{
                  padding: '10px 20px',
                  backgroundColor: '#ff4444',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Break the world
              </button>
            </div>
          } />
        )}

        <Route element={<Dashboard />}>
          <Route path="/dashboard/services" element={<Services />}></Route>
          <Route path="/dashboard/billing" element={<Billing />}></Route>
          <Route path="/dashboard/ai" element={<Ai />}></Route>
          <Route path="/dashboard/affiliate" element={<Affiliate />} />
          <Route path="/dashboard/setting" element={<Setting />} />
          <Route path="/dashboard/support" element={<Support />} />
          <Route path="/dashboard/beli-emas" element={<BeliEmas />} />
        </Route>
      </Routes>
    </Router>
  );
}

export default App;
